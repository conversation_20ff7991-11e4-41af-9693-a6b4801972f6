import Quickshell
import Quickshell.Widgets
import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import Quickshell.Wayland
import Qt5Compat.GraphicalEffects

import "root:/utils"
import "root:/components"

Rectangle {
  id: item

  property int length: config.data.iconSize * pseudoScale + Appearance.data.spacing.small + (config.data.alwaysVisible ? (config.data.iconSize + Appearance.data.spacing.small) : additionalHeight)
  property int breadth: config.data.iconSize + Appearance.data.spacing.small

  // Window preview properties
  property real maxWindowPreviewHeight: 200
  property real maxWindowPreviewWidth: 300
  property real windowControlsHeight: 30
  property bool showPreview: false
  property bool previewReady: false

  width: config.data.orientation == "vertical" ? length : breadth
  height: config.data.orientation == "vertical" ? breadth : length

  color: "transparent"

  // Get desktop entry for the app
  readonly property var desktopEntry: {
    if (modelData.isSeparator === true) return null;
    return DesktopEntries.applications.values.find(app =>
      (app.name && app.name.toLowerCase().includes(modelData.appId.toLowerCase())) ||
      (app.exec && app.exec.toLowerCase().includes(modelData.appId.toLowerCase())) ||
      (app.desktopId && app.desktopId.toLowerCase() === modelData.appId.toLowerCase())
    );
  }

  Timer {
    id: timer
  }

  function delay(height, latestIndex = row.current) {
    timer.interval = Math.abs(index - latestIndex) * Appearance.data.animation.duration.sequential
    timer.repeat = false
    timer.triggered.connect(() => additionalHeight = height)
    timer.start()
  }

  property int additionalHeight: 0
  property real pseudoScale: {
    if (row.current == -1) return 0
    else {
      const falloff = config.data.falloff || 3
      let diff = Math.abs(index - row.current)
      diff = Math.max(0, falloff - diff)
      let damp = falloff - Math.max(1, diff)
      let sc = config.data.scaleFactor
      if (damp) sc /= damp * (config.data.damp || 1)
      diff = diff / falloff * sc
      return diff
    }
  }

  MouseArea {
    id: itemMouseArea
    anchors.fill: parent
    hoverEnabled: true
    acceptedButtons: Qt.LeftButton | Qt.RightButton
    onEntered: {
      row.current = index;
      if (!config.data.alwaysVisible) { window.expand() }
      if (modelData.toplevels && modelData.toplevels.length > 0) {
        showPreview = true;
      }
    }
    onExited: {
      if (row.current == index) {
        row.current = -1;
        if (!config.data.alwaysVisible) { window.collapse(index) }
      }
      showPreview = false;
    }
    onClicked: (mouse) => {
      if (mouse.button === Qt.LeftButton) {
        if (modelData.isSeparator === true) return;

        if (modelData.toplevels && modelData.toplevels.length > 0) {
          // Cycle through windows if multiple
          const nextIndex = (item.lastFocused + 1) % modelData.toplevels.length;
          item.lastFocused = nextIndex;
          modelData.toplevels[nextIndex].activate();
        } else if (desktopEntry) {
          // If no open windows, execute the app
          desktopEntry.execute();
        }
      } else if (mouse.button === Qt.RightButton) {
        if (modelData.isSeparator === true) return;
        contextMenu.popup();
      }
    }
    cursorShape: modelData.isSeparator === true ? Qt.ArrowCursor : Qt.PointingHandCursor
    propagateComposedEvents: true

    property int lastFocused: -1

    ToolTip {
      visible: config.data.showTooltips ? parent.containsMouse : false
      text: {
        if (modelData.isSeparator === true) return "";
        if (desktopEntry && desktopEntry.name) return desktopEntry.name;
        return modelData.appId || "";
      }
      delay: 1000

      contentItem: Text {
        text: parent.text
        font.family: Appearance.data.font.family.regular
        font.pointSize: Appearance.data.font.size.xm
        color: "white"
      }

      background: Rectangle {
        color: "#50000000"
        radius: Appearance.data.rounding.small
      }
    }

    Menu {
      id: contextMenu

      MenuItem {
        text: modelData.pinned === true ? "Unpin" : "Pin"
        onTriggered: {
          if (modelData.pinned === true) {
            window.unpinApp(modelData.appId)
          } else {
            window.pinApp(modelData.appId)
          }
        }
      }
    }

    Column {
      anchors.top: config.data.position == "bottom" ? parent.top : undefined
      anchors.right: config.data.position == "left" ? parent.right : undefined
      anchors.bottom: config.data.position == "top" ? parent.bottom : undefined
      anchors.left: config.data.position == "right" ? parent.left : undefined

      anchors.horizontalCenter: config.data.orientation == "horizontal" ? parent.horizontalCenter : undefined
      anchors.verticalCenter: config.data.orientation == "vertical" ? parent.verticalCenter : undefined

      topPadding: config.data.position == "bottom" ? Appearance.data.spacing.small : undefined
      rightPadding: config.data.position == "left" ? Appearance.data.spacing.small : undefined
      bottomPadding: config.data.position == "top" ? Appearance.data.spacing.small : undefined
      leftPadding: config.data.position == "right" ? Appearance.data.spacing.small : undefined

      width: config.data.orientation == "vertical" ? item.length : config.data.iconSize
      height: config.data.orientation == "vertical" ? config.data.iconSize : item.length

      Item {
        width: config.data.iconSize
        height: width

        // Separator
        Rectangle {
          visible: modelData.isSeparator === true
          width: 2
          height: parent.height * 0.6
          anchors.centerIn: parent
          color: Appearance.getColor("primary")
          opacity: 0.5
        }

        // App icon
        Rectangle {
          visible: modelData.isSeparator !== true
          width: parent.width
          height: width
          color: config.data.showIconsBackground ? Appearance.getColor("background", true) : "transparent"
          radius: Appearance.data.rounding.medium
          border.color: Appearance.getColor("primary")
          border.width: itemMouseArea.containsPress ? 1 : 0

          Image {
            width: parent.width
            height: width
            source: {
              if (modelData.isSeparator === true) return "";
              if (desktopEntry && desktopEntry.icon) {
                return Quickshell.iconPath(desktopEntry.icon);
              }
              return Quickshell.iconPath("application-x-executable");
            }

            transform: Scale {
              origin.x: config.data.iconSize / 2
              origin.y: config.data.iconSize / 2
              xScale: itemMouseArea.pressed ? 0.9 : 1
              yScale: itemMouseArea.pressed ? 0.9 : 1
            }
          }

          // Multiple window indicators (dots like example_quickshell)
          RowLayout {
            visible: modelData.isSeparator !== true && modelData.toplevels && modelData.toplevels.length > 0
            spacing: 3
            anchors {
              top: parent.bottom
              topMargin: 2
              horizontalCenter: parent.horizontalCenter
            }

            Repeater {
              model: Math.min(modelData.toplevels ? modelData.toplevels.length : 0, 3)
              delegate: Rectangle {
                property bool appIsActive: {
                  if (!modelData.toplevels) return false;
                  return modelData.toplevels.find(t => t.activated === true) !== undefined;
                }
                radius: 2
                implicitWidth: (modelData.toplevels && modelData.toplevels.length <= 3) ? 10 : 4
                implicitHeight: 4
                color: appIsActive ? Appearance.getColor("primary") : Appearance.getColor("primary", 0.4)
              }
            }
          }
        }
      }
    }
  }

  // Window preview popup (simplified for now)
  Item {
    id: previewPopup
    visible: showPreview && previewReady && modelData.toplevels && modelData.toplevels.length > 0

    width: previewBackground.implicitWidth + 20
    height: previewBackground.implicitHeight + windowControlsHeight + 20

    anchors.bottom: parent.top
    anchors.horizontalCenter: parent.horizontalCenter
    anchors.bottomMargin: 10

    Rectangle {
      id: previewBackground
      anchors.centerIn: parent
      implicitWidth: previewRow.implicitWidth + 10
      implicitHeight: previewRow.implicitHeight + 10
      color: Appearance.getColor("background", true)
      radius: Appearance.data.rounding.medium
      border.color: Appearance.getColor("primary")
      border.width: 1

      RowLayout {
        id: previewRow
        anchors.centerIn: parent
        spacing: 5

        Repeater {
          model: modelData.toplevels || []
          delegate: Item {
            required property var modelData
            Layout.preferredWidth: Math.min(maxWindowPreviewWidth, 200)
            Layout.preferredHeight: Math.min(maxWindowPreviewHeight, 150) + windowControlsHeight

            MouseArea {
              anchors.fill: parent
              onClicked: {
                parent.modelData?.activate();
                showPreview = false;
              }

              ColumnLayout {
                anchors.fill: parent
                spacing: 2

                // Window title and close button
                Rectangle {
                  Layout.fillWidth: true
                  Layout.preferredHeight: windowControlsHeight
                  color: Appearance.getColor("background")
                  radius: Appearance.data.rounding.small

                  RowLayout {
                    anchors.fill: parent
                    anchors.margins: 5

                    Text {
                      Layout.fillWidth: true
                      text: parent.parent.parent.parent.modelData?.title || "Window"
                      color: Appearance.getColor("foreground")
                      font.pixelSize: Appearance.data.font.size.s
                      elide: Text.ElideRight
                    }

                    Rectangle {
                      Layout.preferredWidth: 20
                      Layout.preferredHeight: 20
                      radius: 10
                      color: Appearance.getColor("primary")

                      Text {
                        anchors.centerIn: parent
                        text: "×"
                        color: "white"
                        font.pixelSize: 12
                      }

                      MouseArea {
                        anchors.fill: parent
                        onClicked: {
                          parent.parent.parent.parent.parent.modelData?.close();
                        }
                      }
                    }
                  }
                }

                // Window preview
                ScreencopyView {
                  id: screencopyView
                  Layout.fillWidth: true
                  Layout.fillHeight: true
                  captureSource: parent.parent.parent.modelData
                  live: true
                  paintCursor: true
                  constraintSize: Qt.size(maxWindowPreviewWidth, maxWindowPreviewHeight)

                  onHasContentChanged: {
                    updatePreviewReadiness();
                  }

                  layer.enabled: true
                  layer.effect: OpacityMask {
                    maskSource: Rectangle {
                      width: screencopyView.width
                      height: screencopyView.height
                      radius: Appearance.data.rounding.small
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  function updatePreviewReadiness() {
    // Check if all screencopy views are ready
    previewReady = true; // Simplified for now
  }

  Timer {
    id: previewTimer
    interval: 500
    onTriggered: {
      if (showPreview) {
        updatePreviewReadiness();
      }
    }
  }

  onShowPreviewChanged: {
    if (showPreview) {
      previewTimer.start();
    } else {
      previewTimer.stop();
      previewReady = false;
    }
  }

  Behavior on length {
    NumberAnimation {
      duration: Appearance.data.animation.duration.normal
      easing.type: Easing.OutBack
    }
  }
}
